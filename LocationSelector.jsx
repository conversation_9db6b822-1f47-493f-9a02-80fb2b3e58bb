// LocationSelector.jsx
import React, { useEffect, useState } from 'react';
import axios from 'axios';

const LocationSelector = () => {
  const [countries, setCountries] = useState([]);
  const [regions, setRegions] = useState([]);
  const [cities, setCities] = useState([]);

  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');

  const API_KEY = 'YOUR_RAPIDAPI_KEY'; // <-- Replace this
  const HEADERS = {
    'X-RapidAPI-Key': API_KEY,
    'X-RapidAPI-Host': 'wft-geo-db.p.rapidapi.com',
  };

  useEffect(() => {
    axios
      .get('https://wft-geo-db.p.rapidapi.com/v1/geo/countries?limit=10', { headers: HEADERS })
      .then((res) => setCountries(res.data.data))
      .catch((err) => console.error('Error fetching countries:', err));
  }, []);

  const fetchRegions = (countryCode) => {
    axios
      .get(`https://wft-geo-db.p.rapidapi.com/v1/geo/countries/${countryCode}/regions`, { headers: HEADERS })
      .then((res) => setRegions(res.data.data))
      .catch((err) => console.error('Error fetching regions:', err));
  };

  const fetchCities = (regionCode) => {
    axios
      .get(`https://wft-geo-db.p.rapidapi.com/v1/geo/regions/${regionCode}/cities?limit=10`, { headers: HEADERS })
      .then((res) => setCities(res.data.data))
      .catch((err) => console.error('Error fetching cities:', err));
  };

  const handleCountryChange = (e) => {
    const code = e.target.value;
    setSelectedCountry(code);
    setSelectedRegion('');
    setCities([]);
    fetchRegions(code);
  };

  const handleRegionChange = (e) => {
    const code = e.target.value;
    setSelectedRegion(code);
    fetchCities(code);
  };

  return (
    <div style={{ backgroundColor: '#111', padding: '2rem', color: '#fff', minHeight: '100vh' }}>
      <h3>Select Your Location</h3>

      <div style={{ display: 'flex', gap: '1rem' }}>
        {/* Country */}
        <select onChange={handleCountryChange} value={selectedCountry}>
          <option value="">Select Country</option>
          {countries.map((c) => (
            <option key={c.code} value={c.code}>
              {c.name}
            </option>
          ))}
        </select>

        {/* Region/Province */}
        <select onChange={handleRegionChange} value={selectedRegion} disabled={!regions.length}>
          <option value="">Select State/Province</option>
          {regions.map((r) => (
            <option key={r.code} value={r.code}>
              {r.name}
            </option>
          ))}
        </select>

        {/* City */}
        <select disabled={!cities.length}>
          <option value="">Select City</option>
          {cities.map((city) => (
            <option key={city.id} value={city.name}>
              {city.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default LocationSelector;
