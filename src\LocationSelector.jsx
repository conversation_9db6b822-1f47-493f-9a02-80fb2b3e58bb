// LocationSelector.jsx
import { useEffect, useState } from 'react';
import axios from 'axios';

const LocationSelector = () => {
  const [countries, setCountries] = useState([]);
  const [regions, setRegions] = useState([]);
  const [cities, setCities] = useState([]);

  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedCity, setSelectedCity] = useState('');

  const [loading, setLoading] = useState({
    countries: false,
    regions: false,
    cities: false
  });
  const [error, setError] = useState('');
  const [apiStatus, setApiStatus] = useState('ready'); // ready, connected, error

  // RapidAPI Configuration
  const API_CONFIG = {
    key: '**************************************************',
    host: 'wft-geo-db.p.rapidapi.com',
    baseUrl: 'https://wft-geo-db.p.rapidapi.com/v1/geo',
    headers: {
      'X-RapidAPI-Key': '**************************************************',
      'X-RapidAPI-Host': 'wft-geo-db.p.rapidapi.com'
      // Removed Content-Type header as it can cause CORS issues with GET requests
    }
  };

  // Mock data fallback in case API fails
  const MOCK_COUNTRIES = [
    { code: 'US', name: 'United States' },
    { code: 'CA', name: 'Canada' },
    { code: 'GB', name: 'United Kingdom' },
    { code: 'DE', name: 'Germany' },
    { code: 'FR', name: 'France' },
    { code: 'JP', name: 'Japan' },
    { code: 'AU', name: 'Australia' },
    { code: 'BR', name: 'Brazil' },
    { code: 'IN', name: 'India' },
    { code: 'CN', name: 'China' }
  ];

  const MOCK_REGIONS = {
    'US': [
      { code: 'CA', name: 'California' },
      { code: 'NY', name: 'New York' },
      { code: 'TX', name: 'Texas' },
      { code: 'FL', name: 'Florida' }
    ],
    'CA': [
      { code: 'ON', name: 'Ontario' },
      { code: 'BC', name: 'British Columbia' },
      { code: 'QC', name: 'Quebec' },
      { code: 'AB', name: 'Alberta' }
    ],
    'GB': [
      { code: 'ENG', name: 'England' },
      { code: 'SCT', name: 'Scotland' },
      { code: 'WLS', name: 'Wales' },
      { code: 'NIR', name: 'Northern Ireland' }
    ]
  };

  const MOCK_CITIES = {
    'CA': [
      { id: 1, name: 'Los Angeles' },
      { id: 2, name: 'San Francisco' },
      { id: 3, name: 'San Diego' },
      { id: 4, name: 'Sacramento' }
    ],
    'NY': [
      { id: 5, name: 'New York City' },
      { id: 6, name: 'Buffalo' },
      { id: 7, name: 'Rochester' },
      { id: 8, name: 'Albany' }
    ],
    'ON': [
      { id: 9, name: 'Toronto' },
      { id: 10, name: 'Ottawa' },
      { id: 11, name: 'Hamilton' },
      { id: 12, name: 'London' }
    ]
  };

  useEffect(() => {
    const fetchCountriesWithFallback = async () => {
      setLoading(prev => ({ ...prev, countries: true }));
      setError('');
      setApiStatus('connecting');

      try {
        // Try with a smaller limit first to avoid rate limiting
        const response = await axios.get(`${API_CONFIG.baseUrl}/countries?limit=20`, {
          headers: API_CONFIG.headers,
          timeout: 8000 // Reduced timeout
        });

        if (response.data && response.data.data) {
          setCountries(response.data.data);
          setApiStatus('connected');
          setError(''); // Clear any previous errors
        } else {
          throw new Error('No data received from API');
        }
      } catch (err) {
        console.error('Error fetching countries:', err);
        setApiStatus('error');

        let errorMessage = 'API temporarily unavailable. ';
        let useFallback = false;

        if (err.response?.status === 429) {
          errorMessage += 'Rate limit exceeded. Using offline data.';
          useFallback = true;
        } else if (err.response?.status === 403) {
          errorMessage += 'API access denied. Using offline data.';
          useFallback = true;
        } else if (err.code === 'ECONNABORTED') {
          errorMessage += 'Request timeout. Using offline data.';
          useFallback = true;
        } else {
          errorMessage += 'Using offline data.';
          useFallback = true;
        }

        if (useFallback) {
          setCountries(MOCK_COUNTRIES);
          setApiStatus('offline');
          errorMessage += ' Limited functionality available.';
        }

        setError(errorMessage);
      } finally {
        setLoading(prev => ({ ...prev, countries: false }));
      }
    };

    fetchCountriesWithFallback();
  }, []);

  const fetchRegions = async (countryCode) => {
    setLoading(prev => ({ ...prev, regions: true }));
    setError('');

    try {
      const response = await axios.get(`${API_CONFIG.baseUrl}/countries/${countryCode}/regions`, {
        headers: API_CONFIG.headers,
        timeout: 5000 // Reduced timeout for faster fallback
      });

      if (response.data && response.data.data && response.data.data.length > 0) {
        setRegions(response.data.data);
      } else {
        // Use mock data if no regions found
        const mockRegions = MOCK_REGIONS[countryCode] || [];
        setRegions(mockRegions);
        if (mockRegions.length === 0) {
          setError('No regions available for this country.');
        }
      }
    } catch (err) {
      console.error('Error fetching regions:', err);

      // Always try to use mock data as fallback
      const mockRegions = MOCK_REGIONS[countryCode] || [];
      setRegions(mockRegions);

      if (mockRegions.length > 0) {
        setError('Using offline region data due to API issues.');
      } else {
        let errorMessage = 'No regions available. ';
        if (err.response?.status === 429) {
          errorMessage += 'Rate limit exceeded.';
        } else if (err.response?.status === 403) {
          errorMessage += 'API access denied.';
        } else {
          errorMessage += 'API temporarily unavailable.';
        }
        setError(errorMessage);
      }
    } finally {
      setLoading(prev => ({ ...prev, regions: false }));
    }
  };

  const fetchCities = async (regionCode) => {
    setLoading(prev => ({ ...prev, cities: true }));
    setError('');

    try {
      const response = await axios.get(`${API_CONFIG.baseUrl}/regions/${regionCode}/cities?limit=20`, {
        headers: API_CONFIG.headers,
        timeout: 5000 // Reduced timeout for faster fallback
      });

      if (response.data && response.data.data && response.data.data.length > 0) {
        setCities(response.data.data);
      } else {
        // Use mock data if no cities found
        const mockCities = MOCK_CITIES[regionCode] || [];
        setCities(mockCities);
        if (mockCities.length === 0) {
          setError('No cities available for this region.');
        }
      }
    } catch (err) {
      console.error('Error fetching cities:', err);

      // Always try to use mock data as fallback
      const mockCities = MOCK_CITIES[regionCode] || [];
      setCities(mockCities);

      if (mockCities.length > 0) {
        setError('Using offline city data due to API issues.');
      } else {
        let errorMessage = 'No cities available. ';
        if (err.response?.status === 429) {
          errorMessage += 'Rate limit exceeded.';
        } else if (err.response?.status === 403) {
          errorMessage += 'API access denied.';
        } else {
          errorMessage += 'API temporarily unavailable.';
        }
        setError(errorMessage);
      }
    } finally {
      setLoading(prev => ({ ...prev, cities: false }));
    }
  };

  const handleCountryChange = (e) => {
    const code = e.target.value;
    setSelectedCountry(code);
    setSelectedRegion('');
    setSelectedCity('');
    setRegions([]);
    setCities([]);
    if (code) {
      fetchRegions(code);
    }
  };

  const handleRegionChange = (e) => {
    const code = e.target.value;
    setSelectedRegion(code);
    setSelectedCity('');
    setCities([]);
    if (code) {
      fetchCities(code);
    }
  };

  const handleCityChange = (e) => {
    setSelectedCity(e.target.value);
  };

  return (
    <div style={{ backgroundColor: '#111', padding: '2rem', color: '#fff', minHeight: '100vh' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
        <h3>Select Your Location</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span style={{ fontSize: '0.9rem' }}>RapidAPI Status:</span>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: apiStatus === 'connected' ? '#4CAF50' :
                           apiStatus === 'connecting' ? '#FFC107' :
                           apiStatus === 'offline' ? '#FF9800' : '#F44336'
          }}></div>
          <span style={{ fontSize: '0.8rem', opacity: 0.8 }}>
            {apiStatus === 'connected' ? 'Connected' :
             apiStatus === 'connecting' ? 'Connecting...' :
             apiStatus === 'offline' ? 'Offline Mode' : 'Error'}
          </span>
        </div>
      </div>

      {error && (
        <div style={{
          backgroundColor: apiStatus === 'offline' ? '#FF9800' : '#ff4444',
          color: 'white',
          padding: '1rem',
          borderRadius: '4px',
          marginBottom: '1rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <span>{error}</span>
          {(apiStatus === 'error' || apiStatus === 'offline') && (
            <button
              onClick={() => window.location.reload()}
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                border: 'none',
                color: 'white',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '0.9rem'
              }}
            >
              Retry
            </button>
          )}
        </div>
      )}

      <div style={{ display: 'flex', gap: '1rem' }}>
        {/* Country */}
        <select onChange={handleCountryChange} value={selectedCountry} disabled={loading.countries}>
          <option value="">{loading.countries ? 'Loading countries...' : 'Select Country'}</option>
          {countries.map((c) => (
            <option key={c.code} value={c.code}>
              {c.name}
            </option>
          ))}
        </select>

        {/* Region/Province */}
        <select
          onChange={handleRegionChange}
          value={selectedRegion}
          disabled={!regions.length || loading.regions}
        >
          <option value="">
            {loading.regions ? 'Loading regions...' : 'Select State/Province'}
          </option>
          {regions.map((r) => (
            <option key={r.code} value={r.code}>
              {r.name}
            </option>
          ))}
        </select>

        {/* City */}
        <select
          onChange={handleCityChange}
          value={selectedCity}
          disabled={!cities.length || loading.cities}
        >
          <option value="">
            {loading.cities ? 'Loading cities...' : 'Select City'}
          </option>
          {cities.map((city) => (
            <option key={city.id} value={city.name}>
              {city.name}
            </option>
          ))}
        </select>
      </div>

      {/* Selected Location Display */}
      {(selectedCountry || selectedRegion || selectedCity) && (
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          backgroundColor: '#333',
          borderRadius: '4px',
          border: '1px solid #555'
        }}>
          <h4 style={{ margin: '0 0 1rem 0', color: '#4CAF50' }}>Selected Location:</h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            {selectedCountry && (
              <div>
                <strong>Country:</strong> {countries.find(c => c.code === selectedCountry)?.name || selectedCountry}
              </div>
            )}
            {selectedRegion && (
              <div>
                <strong>Region:</strong> {regions.find(r => r.code === selectedRegion)?.name || selectedRegion}
              </div>
            )}
            {selectedCity && (
              <div>
                <strong>City:</strong> {selectedCity}
              </div>
            )}
          </div>
        </div>
      )}

      {/* API Information */}
      <div style={{
        marginTop: '2rem',
        padding: '1rem',
        backgroundColor: '#222',
        borderRadius: '4px',
        fontSize: '0.9rem',
        opacity: 0.8
      }}>
        <div><strong>API Provider:</strong> RapidAPI - GeoDB Cities</div>
        <div><strong>API Host:</strong> {API_CONFIG.host}</div>
        <div><strong>Countries Loaded:</strong> {countries.length}</div>
        <div><strong>Regions Available:</strong> {regions.length}</div>
        <div><strong>Cities Available:</strong> {cities.length}</div>
      </div>
    </div>
  );
};

export default LocationSelector;
