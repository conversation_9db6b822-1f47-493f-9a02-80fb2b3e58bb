// LocationSelector.jsx
import { useEffect, useState } from 'react';
import axios from 'axios';

const LocationSelector = () => {
  const [countries, setCountries] = useState([]);
  const [regions, setRegions] = useState([]);
  const [cities, setCities] = useState([]);

  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedCity, setSelectedCity] = useState('');

  const [loading, setLoading] = useState({
    countries: false,
    regions: false,
    cities: false
  });
  const [error, setError] = useState('');
  const [apiStatus, setApiStatus] = useState('ready'); // ready, connected, error

  // RapidAPI Configuration - Exact format from RapidAPI
  const options = {
    method: 'GET',
    headers: {
      'x-rapidapi-key': '**************************************************',
      'x-rapidapi-host': 'wft-geo-db.p.rapidapi.com'
    }
  };

  useEffect(() => {
    const fetchCountries = async () => {
      setLoading(prev => ({ ...prev, countries: true }));
      setError('');
      setApiStatus('connecting');

      try {
        // Use exact RapidAPI format
        const response = await axios.request({
          ...options,
          url: 'https://wft-geo-db.p.rapidapi.com/v1/geo/countries'
        });

        if (response.data && response.data.data) {
          setCountries(response.data.data);
          setApiStatus('connected');
          setError(''); // Clear any previous errors
        } else {
          throw new Error('No data received from API');
        }
      } catch (err) {
        console.error('Error fetching countries:', err);
        setApiStatus('error');

        let errorMessage = 'Failed to load countries. ';

        if (err.response?.status === 429) {
          errorMessage += 'Rate limit exceeded. Please try again later.';
        } else if (err.response?.status === 403) {
          errorMessage += 'API access denied. Please check your API key.';
        } else if (err.code === 'ECONNABORTED') {
          errorMessage += 'Request timeout. Please check your connection.';
        } else {
          errorMessage += 'Please try again.';
        }

        setError(errorMessage);
      } finally {
        setLoading(prev => ({ ...prev, countries: false }));
      }
    };

    fetchCountries();
  }, []);

  const fetchRegions = async (countryCode) => {
    setLoading(prev => ({ ...prev, regions: true }));
    setError('');

    try {
      // Use exact RapidAPI format
      const response = await axios.request({
        ...options,
        url: `https://wft-geo-db.p.rapidapi.com/v1/geo/countries/${countryCode}/regions`
      });

      if (response.data && response.data.data) {
        setRegions(response.data.data);
      } else {
        setRegions([]);
        setError('No regions found for this country.');
      }
    } catch (err) {
      console.error('Error fetching regions:', err);
      setRegions([]);

      let errorMessage = 'Failed to load regions. ';
      if (err.response?.status === 429) {
        errorMessage += 'Rate limit exceeded. Please try again later.';
      } else if (err.response?.status === 403) {
        errorMessage += 'API access denied. Please check your API key.';
      } else if (err.response?.status === 404) {
        errorMessage += 'No regions found for this country.';
      } else {
        errorMessage += 'Please try again.';
      }
      setError(errorMessage);
    } finally {
      setLoading(prev => ({ ...prev, regions: false }));
    }
  };

  const fetchCities = async (regionCode) => {
    setLoading(prev => ({ ...prev, cities: true }));
    setError('');

    try {
      // Use exact RapidAPI format
      const response = await axios.request({
        ...options,
        url: `https://wft-geo-db.p.rapidapi.com/v1/geo/regions/${regionCode}/cities`
      });

      if (response.data && response.data.data) {
        setCities(response.data.data);
      } else {
        setCities([]);
        setError('No cities found for this region.');
      }
    } catch (err) {
      console.error('Error fetching cities:', err);
      setCities([]);

      let errorMessage = 'Failed to load cities. ';
      if (err.response?.status === 429) {
        errorMessage += 'Rate limit exceeded. Please try again later.';
      } else if (err.response?.status === 403) {
        errorMessage += 'API access denied. Please check your API key.';
      } else if (err.response?.status === 404) {
        errorMessage += 'No cities found for this region.';
      } else {
        errorMessage += 'Please try again.';
      }
      setError(errorMessage);
    } finally {
      setLoading(prev => ({ ...prev, cities: false }));
    }
  };

  const handleCountryChange = (e) => {
    const code = e.target.value;
    setSelectedCountry(code);
    setSelectedRegion('');
    setSelectedCity('');
    setRegions([]);
    setCities([]);
    if (code) {
      fetchRegions(code);
    }
  };

  const handleRegionChange = (e) => {
    const code = e.target.value;
    setSelectedRegion(code);
    setSelectedCity('');
    setCities([]);
    if (code) {
      fetchCities(code);
    }
  };

  const handleCityChange = (e) => {
    setSelectedCity(e.target.value);
  };

  return (
    <div style={{ backgroundColor: '#111', padding: '2rem', color: '#fff', minHeight: '100vh' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
        <h3>Select Your Location</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span style={{ fontSize: '0.9rem' }}>RapidAPI Status:</span>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: apiStatus === 'connected' ? '#4CAF50' :
                           apiStatus === 'connecting' ? '#FFC107' : '#F44336'
          }}></div>
          <span style={{ fontSize: '0.8rem', opacity: 0.8 }}>
            {apiStatus === 'connected' ? 'Connected' :
             apiStatus === 'connecting' ? 'Connecting...' : 'Error'}
          </span>
        </div>
      </div>

      {error && (
        <div style={{
          backgroundColor: '#ff4444',
          color: 'white',
          padding: '1rem',
          borderRadius: '4px',
          marginBottom: '1rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <span>{error}</span>
          {apiStatus === 'error' && (
            <button
              onClick={() => window.location.reload()}
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                border: 'none',
                color: 'white',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '0.9rem'
              }}
            >
              Retry
            </button>
          )}
        </div>
      )}

      <div style={{ display: 'flex', gap: '1rem' }}>
        {/* Country */}
        <select onChange={handleCountryChange} value={selectedCountry} disabled={loading.countries}>
          <option value="">{loading.countries ? 'Loading countries...' : 'Select Country'}</option>
          {countries.map((c) => (
            <option key={c.code} value={c.code}>
              {c.name}
            </option>
          ))}
        </select>

        {/* Region/Province */}
        <select
          onChange={handleRegionChange}
          value={selectedRegion}
          disabled={!regions.length || loading.regions}
        >
          <option value="">
            {loading.regions ? 'Loading regions...' : 'Select State/Province'}
          </option>
          {regions.map((r) => (
            <option key={r.code} value={r.code}>
              {r.name}
            </option>
          ))}
        </select>

        {/* City */}
        <select
          onChange={handleCityChange}
          value={selectedCity}
          disabled={!cities.length || loading.cities}
        >
          <option value="">
            {loading.cities ? 'Loading cities...' : 'Select City'}
          </option>
          {cities.map((city) => (
            <option key={city.id} value={city.name}>
              {city.name}
            </option>
          ))}
        </select>
      </div>

      {/* Selected Location Display */}
      {(selectedCountry || selectedRegion || selectedCity) && (
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          backgroundColor: '#333',
          borderRadius: '4px',
          border: '1px solid #555'
        }}>
          <h4 style={{ margin: '0 0 1rem 0', color: '#4CAF50' }}>Selected Location:</h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            {selectedCountry && (
              <div>
                <strong>Country:</strong> {countries.find(c => c.code === selectedCountry)?.name || selectedCountry}
              </div>
            )}
            {selectedRegion && (
              <div>
                <strong>Region:</strong> {regions.find(r => r.code === selectedRegion)?.name || selectedRegion}
              </div>
            )}
            {selectedCity && (
              <div>
                <strong>City:</strong> {selectedCity}
              </div>
            )}
          </div>
        </div>
      )}

      {/* API Information */}
      <div style={{
        marginTop: '2rem',
        padding: '1rem',
        backgroundColor: '#222',
        borderRadius: '4px',
        fontSize: '0.9rem',
        opacity: 0.8
      }}>
        <div><strong>API Provider:</strong> RapidAPI - GeoDB Cities</div>
        <div><strong>API Host:</strong> wft-geo-db.p.rapidapi.com</div>
        <div><strong>Countries Loaded:</strong> {countries.length}</div>
        <div><strong>Regions Available:</strong> {regions.length}</div>
        <div><strong>Cities Available:</strong> {cities.length}</div>
      </div>
    </div>
  );
};

export default LocationSelector;
