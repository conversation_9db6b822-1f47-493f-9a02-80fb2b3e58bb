// LocationSelector.jsx
import { useEffect, useState } from 'react';
import axios from 'axios';

const LocationSelector = () => {
  const [countries, setCountries] = useState([]);
  const [regions, setRegions] = useState([]);
  const [cities, setCities] = useState([]);

  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');

  const [loading, setLoading] = useState({
    countries: false,
    regions: false,
    cities: false
  });
  const [error, setError] = useState('');

  const API_KEY = '6f60bc1fcdmshdcc6cb9d20e912ep1ff024jsn5c05ec817e23'; // <-- Replace this
  const HEADERS = {
    'X-RapidAPI-Key': API_KEY,
    'X-RapidAPI-Host': 'wft-geo-db.p.rapidapi.com',
  };

  useEffect(() => {
    setLoading(prev => ({ ...prev, countries: true }));
    setError('');

    axios
      .get('https://wft-geo-db.p.rapidapi.com/v1/geo/countries?limit=10', { headers: HEADERS })
      .then((res) => {
        setCountries(res.data.data);
        setLoading(prev => ({ ...prev, countries: false }));
      })
      .catch((err) => {
        console.error('Error fetching countries:', err);
        setError('Failed to load countries. Please try again.');
        setLoading(prev => ({ ...prev, countries: false }));
      });
  }, []);

  const fetchRegions = (countryCode) => {
    setLoading(prev => ({ ...prev, regions: true }));
    setError('');

    axios
      .get(`https://wft-geo-db.p.rapidapi.com/v1/geo/countries/${countryCode}/regions`, { headers: HEADERS })
      .then((res) => {
        setRegions(res.data.data || []);
        setLoading(prev => ({ ...prev, regions: false }));
      })
      .catch((err) => {
        console.error('Error fetching regions:', err);
        setError('Failed to load regions. Please try again.');
        setRegions([]);
        setLoading(prev => ({ ...prev, regions: false }));
      });
  };

  const fetchCities = (regionCode) => {
    setLoading(prev => ({ ...prev, cities: true }));
    setError('');

    axios
      .get(`https://wft-geo-db.p.rapidapi.com/v1/geo/regions/${regionCode}/cities?limit=10`, { headers: HEADERS })
      .then((res) => {
        setCities(res.data.data || []);
        setLoading(prev => ({ ...prev, cities: false }));
      })
      .catch((err) => {
        console.error('Error fetching cities:', err);
        setError('Failed to load cities. Please try again.');
        setCities([]);
        setLoading(prev => ({ ...prev, cities: false }));
      });
  };

  const handleCountryChange = (e) => {
    const code = e.target.value;
    setSelectedCountry(code);
    setSelectedRegion('');
    setCities([]);
    fetchRegions(code);
  };

  const handleRegionChange = (e) => {
    const code = e.target.value;
    setSelectedRegion(code);
    fetchCities(code);
  };

  return (
    <div style={{ backgroundColor: '#111', padding: '2rem', color: '#fff', minHeight: '100vh' }}>
      <h3>Select Your Location</h3>

      {error && (
        <div style={{
          backgroundColor: '#ff4444',
          color: 'white',
          padding: '1rem',
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          {error}
        </div>
      )}

      <div style={{ display: 'flex', gap: '1rem' }}>
        {/* Country */}
        <select onChange={handleCountryChange} value={selectedCountry} disabled={loading.countries}>
          <option value="">{loading.countries ? 'Loading countries...' : 'Select Country'}</option>
          {countries.map((c) => (
            <option key={c.code} value={c.code}>
              {c.name}
            </option>
          ))}
        </select>

        {/* Region/Province */}
        <select
          onChange={handleRegionChange}
          value={selectedRegion}
          disabled={!regions.length || loading.regions}
        >
          <option value="">
            {loading.regions ? 'Loading regions...' : 'Select State/Province'}
          </option>
          {regions.map((r) => (
            <option key={r.code} value={r.code}>
              {r.name}
            </option>
          ))}
        </select>

        {/* City */}
        <select disabled={!cities.length || loading.cities}>
          <option value="">
            {loading.cities ? 'Loading cities...' : 'Select City'}
          </option>
          {cities.map((city) => (
            <option key={city.id} value={city.name}>
              {city.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default LocationSelector;
