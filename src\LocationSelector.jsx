// LocationSelector.jsx
import { useEffect, useState } from 'react';
import axios from 'axios';

const LocationSelector = () => {
  const [countries, setCountries] = useState([]);
  const [regions, setRegions] = useState([]);
  const [cities, setCities] = useState([]);

  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedCity, setSelectedCity] = useState('');

  const [loading, setLoading] = useState({
    countries: false,
    regions: false,
    cities: false
  });
  const [error, setError] = useState('');
  const [apiStatus, setApiStatus] = useState('ready'); // ready, connected, error

  // RapidAPI Configuration
  const API_CONFIG = {
    key: '**************************************************',
    host: 'wft-geo-db.p.rapidapi.com',
    baseUrl: 'https://wft-geo-db.p.rapidapi.com/v1/geo',
    headers: {
      'X-RapidAPI-Key': '**************************************************',
      'X-RapidAPI-Host': 'wft-geo-db.p.rapidapi.com',
      'Content-Type': 'application/json'
    }
  };

  useEffect(() => {
    setLoading(prev => ({ ...prev, countries: true }));
    setError('');
    setApiStatus('connecting');

    axios
      .get(`${API_CONFIG.baseUrl}/countries?limit=50`, {
        headers: API_CONFIG.headers,
        timeout: 10000 // 10 second timeout
      })
      .then((res) => {
        if (res.data && res.data.data) {
          setCountries(res.data.data);
          setApiStatus('connected');
        } else {
          setError('No countries data received from API.');
          setApiStatus('error');
        }
        setLoading(prev => ({ ...prev, countries: false }));
      })
      .catch((err) => {
        console.error('Error fetching countries:', err);
        setApiStatus('error');
        let errorMessage = 'Failed to load countries. ';

        if (err.response?.status === 429) {
          errorMessage += 'Rate limit exceeded. Please try again later.';
        } else if (err.response?.status === 403) {
          errorMessage += 'API access denied. Please check your API key.';
        } else if (err.code === 'ECONNABORTED') {
          errorMessage += 'Request timeout. Please check your connection.';
        } else {
          errorMessage += 'Please try again.';
        }

        setError(errorMessage);
        setLoading(prev => ({ ...prev, countries: false }));
      });
  }, []);

  const fetchRegions = (countryCode) => {
    setLoading(prev => ({ ...prev, regions: true }));
    setError('');

    axios
      .get(`${API_CONFIG.baseUrl}/countries/${countryCode}/regions`, {
        headers: API_CONFIG.headers,
        timeout: 10000
      })
      .then((res) => {
        if (res.data && res.data.data) {
          setRegions(res.data.data);
        } else {
          setRegions([]);
          setError('No regions found for this country.');
        }
        setLoading(prev => ({ ...prev, regions: false }));
      })
      .catch((err) => {
        console.error('Error fetching regions:', err);
        let errorMessage = 'Failed to load regions. ';

        if (err.response?.status === 429) {
          errorMessage += 'Rate limit exceeded. Please try again later.';
        } else if (err.response?.status === 404) {
          errorMessage += 'No regions found for this country.';
        } else if (err.response?.status === 403) {
          errorMessage += 'API access denied. Please check your API key.';
        } else {
          errorMessage += 'Please try again.';
        }

        setError(errorMessage);
        setRegions([]);
        setLoading(prev => ({ ...prev, regions: false }));
      });
  };

  const fetchCities = (regionCode) => {
    setLoading(prev => ({ ...prev, cities: true }));
    setError('');

    axios
      .get(`${API_CONFIG.baseUrl}/regions/${regionCode}/cities?limit=50`, {
        headers: API_CONFIG.headers,
        timeout: 10000
      })
      .then((res) => {
        if (res.data && res.data.data) {
          setCities(res.data.data);
        } else {
          setCities([]);
          setError('No cities found for this region.');
        }
        setLoading(prev => ({ ...prev, cities: false }));
      })
      .catch((err) => {
        console.error('Error fetching cities:', err);
        let errorMessage = 'Failed to load cities. ';

        if (err.response?.status === 429) {
          errorMessage += 'Rate limit exceeded. Please try again later.';
        } else if (err.response?.status === 404) {
          errorMessage += 'No cities found for this region.';
        } else if (err.response?.status === 403) {
          errorMessage += 'API access denied. Please check your API key.';
        } else {
          errorMessage += 'Please try again.';
        }

        setError(errorMessage);
        setCities([]);
        setLoading(prev => ({ ...prev, cities: false }));
      });
  };

  const handleCountryChange = (e) => {
    const code = e.target.value;
    setSelectedCountry(code);
    setSelectedRegion('');
    setSelectedCity('');
    setRegions([]);
    setCities([]);
    if (code) {
      fetchRegions(code);
    }
  };

  const handleRegionChange = (e) => {
    const code = e.target.value;
    setSelectedRegion(code);
    setSelectedCity('');
    setCities([]);
    if (code) {
      fetchCities(code);
    }
  };

  const handleCityChange = (e) => {
    setSelectedCity(e.target.value);
  };

  return (
    <div style={{ backgroundColor: '#111', padding: '2rem', color: '#fff', minHeight: '100vh' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
        <h3>Select Your Location</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span style={{ fontSize: '0.9rem' }}>RapidAPI Status:</span>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: apiStatus === 'connected' ? '#4CAF50' :
                           apiStatus === 'connecting' ? '#FFC107' : '#F44336'
          }}></div>
          <span style={{ fontSize: '0.8rem', opacity: 0.8 }}>
            {apiStatus === 'connected' ? 'Connected' :
             apiStatus === 'connecting' ? 'Connecting...' : 'Error'}
          </span>
        </div>
      </div>

      {error && (
        <div style={{
          backgroundColor: '#ff4444',
          color: 'white',
          padding: '1rem',
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          {error}
        </div>
      )}

      <div style={{ display: 'flex', gap: '1rem' }}>
        {/* Country */}
        <select onChange={handleCountryChange} value={selectedCountry} disabled={loading.countries}>
          <option value="">{loading.countries ? 'Loading countries...' : 'Select Country'}</option>
          {countries.map((c) => (
            <option key={c.code} value={c.code}>
              {c.name}
            </option>
          ))}
        </select>

        {/* Region/Province */}
        <select
          onChange={handleRegionChange}
          value={selectedRegion}
          disabled={!regions.length || loading.regions}
        >
          <option value="">
            {loading.regions ? 'Loading regions...' : 'Select State/Province'}
          </option>
          {regions.map((r) => (
            <option key={r.code} value={r.code}>
              {r.name}
            </option>
          ))}
        </select>

        {/* City */}
        <select
          onChange={handleCityChange}
          value={selectedCity}
          disabled={!cities.length || loading.cities}
        >
          <option value="">
            {loading.cities ? 'Loading cities...' : 'Select City'}
          </option>
          {cities.map((city) => (
            <option key={city.id} value={city.name}>
              {city.name}
            </option>
          ))}
        </select>
      </div>

      {/* Selected Location Display */}
      {(selectedCountry || selectedRegion || selectedCity) && (
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          backgroundColor: '#333',
          borderRadius: '4px',
          border: '1px solid #555'
        }}>
          <h4 style={{ margin: '0 0 1rem 0', color: '#4CAF50' }}>Selected Location:</h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            {selectedCountry && (
              <div>
                <strong>Country:</strong> {countries.find(c => c.code === selectedCountry)?.name || selectedCountry}
              </div>
            )}
            {selectedRegion && (
              <div>
                <strong>Region:</strong> {regions.find(r => r.code === selectedRegion)?.name || selectedRegion}
              </div>
            )}
            {selectedCity && (
              <div>
                <strong>City:</strong> {selectedCity}
              </div>
            )}
          </div>
        </div>
      )}

      {/* API Information */}
      <div style={{
        marginTop: '2rem',
        padding: '1rem',
        backgroundColor: '#222',
        borderRadius: '4px',
        fontSize: '0.9rem',
        opacity: 0.8
      }}>
        <div><strong>API Provider:</strong> RapidAPI - GeoDB Cities</div>
        <div><strong>API Host:</strong> {API_CONFIG.host}</div>
        <div><strong>Countries Loaded:</strong> {countries.length}</div>
        <div><strong>Regions Available:</strong> {regions.length}</div>
        <div><strong>Cities Available:</strong> {cities.length}</div>
      </div>
    </div>
  );
};

export default LocationSelector;
