<!DOCTYPE html>
<html>
<head>
    <title>RapidAPI Test</title>
</head>
<body>
    <h1>RapidAPI Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            const options = {
                method: 'GET',
                url: 'https://wft-geo-db.p.rapidapi.com/v1/geo/countries',
                headers: {
                    'x-rapidapi-key': '**************************************************',
                    'x-rapidapi-host': 'wft-geo-db.p.rapidapi.com'
                }
            };

            try {
                const response = await axios.request(options);
                resultDiv.innerHTML = `
                    <h3>Success!</h3>
                    <p>Status: ${response.status}</p>
                    <p>Countries found: ${response.data.data ? response.data.data.length : 0}</p>
                    <pre>${JSON.stringify(response.data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error!</h3>
                    <p>Status: ${error.response?.status || 'Unknown'}</p>
                    <p>Message: ${error.message}</p>
                    <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                `;
            }
        }
    </script>
</body>
</html>
